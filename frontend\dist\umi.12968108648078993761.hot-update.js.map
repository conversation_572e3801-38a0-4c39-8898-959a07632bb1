{"version": 3, "sources": ["umi.12968108648078993761.hot-update.js", "src/types/api.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='16026353188784898241';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * API 相关类型定义\n * 基于后端 DTO 和 Entity 类生成\n */\n\n// ============= 基础类型 =============\n\n/**\n * API 响应基础结构\n */\nexport interface ApiResponse<T = any> {\n  code: number;\n  message: string;\n  data: T;\n  timestamp: string;\n}\n\n/**\n * 分页请求参数\n */\nexport interface PageRequest {\n  current?: number;\n  pageSize?: number;\n}\n\n/**\n * 分页响应结构\n */\nexport interface PageResponse<T> {\n  list: T[];\n  total: number;\n  current: number;\n  pageSize: number;\n}\n\n// ============= 认证相关类型 =============\n\n/**\n * 登录请求（验证码登录）\n */\nexport interface LoginRequest {\n  email: string;\n  code: string; // 验证码\n}\n\n/**\n * 发送验证码请求\n */\nexport interface SendVerificationCodeRequest {\n  email: string;\n  type: 'login' | 'register';\n}\n\n/**\n * 发送验证码响应\n */\nexport interface SendVerificationCodeResponse {\n  success: boolean;\n  message: string;\n  expireMinutes?: number;\n  nextSendTime?: number; // 下次可发送时间（秒）\n\n}\n\n// 注册功能已移除，统一使用验证码登录/注册流程\n\n/**\n * 用户信息\n */\nexport interface UserInfo {\n  id: number;\n  email: string;\n  name: string;\n}\n\n/**\n * 团队信息（登录响应中的简化版本）\n */\nexport interface TeamInfo {\n  id: number;\n  name: string;\n  /** @deprecated 使用 role 字段替代 */\n  isCreator: boolean;\n  role?: TeamRole; // 设为可选，以防后端暂时没有返回\n  memberCount: number;\n  lastAccessTime: string;\n}\n\n/**\n * 登录响应\n */\nexport interface LoginResponse {\n  token: string;\n  expiresIn: number;\n  user: UserInfo;\n  teams: TeamInfo[];\n  /** 当前选择的团队信息（用于团队选择响应） */\n  team?: TeamInfo;\n  /** 团队选择成功标识（用于团队选择响应） */\n  teamSelectionSuccess?: boolean;\n}\n\n// ============= 团队管理相关类型 =============\n\n/**\n * 创建团队请求\n */\nexport interface CreateTeamRequest {\n  name: string;\n  description?: string;\n}\n\n/**\n * 更新团队请求\n */\nexport interface UpdateTeamRequest {\n  name: string;\n  description?: string;\n}\n\n/**\n * 邀请成员请求\n */\nexport interface InviteMembersRequest {\n  emails: string[];\n  message?: string;\n}\n\n/**\n * 团队详情响应\n */\nexport interface TeamDetailResponse {\n  id: number;\n  name: string;\n  description?: string;\n  createdBy: number;\n  memberCount: number;\n  /** @deprecated 使用 role 字段替代 */\n  isCreator: boolean;\n  role?: TeamRole; // 设为可选，以防后端暂时没有返回\n  lastAccessTime?: string;\n  assignedAt?: string; // 用户加入团队的时间\n  isActive?: boolean; // 用户在团队中的状态\n  createdAt: string;\n  updatedAt: string;\n  stats?: TeamStatsData; // 可选的统计数据\n}\n\n/**\n * 团队成员响应\n */\nexport interface TeamMemberResponse {\n  id: number;\n  accountId: number;\n  email: string;\n  name: string;\n  /** @deprecated 使用 role 字段替代 */\n  isCreator: boolean;\n  role?: TeamRole; // 设为可选，以防后端暂时没有返回\n  assignedAt: string;\n  lastAccessTime: string;\n  isActive: boolean;\n}\n\n// ============= 用户管理相关类型 =============\n\n/**\n * 用户资料响应\n */\nexport interface UserProfileResponse {\n  id: number;\n  email: string;\n  name: string;\n  telephone?: string;\n  defaultSubscriptionPlanId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 更新用户资料请求\n */\nexport interface UpdateUserProfileRequest {\n  name?: string;\n  telephone?: string;\n  currentPassword?: string;\n  newPassword?: string;\n}\n\n// ============= 订阅管理相关类型 =============\n\n/**\n * 订阅套餐响应\n */\nexport interface SubscriptionPlanResponse {\n  id: number;\n  name: string;\n  description: string;\n  maxSize: number;\n  price: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 创建订阅请求\n */\nexport interface CreateSubscriptionRequest {\n  planId: number;\n  duration: number;\n}\n\n/**\n * 订阅状态枚举\n */\nexport enum SubscriptionStatus {\n  ACTIVE = 'ACTIVE',\n  EXPIRED = 'EXPIRED',\n  CANCELLED = 'CANCELLED',\n}\n\n/**\n * 订阅响应\n */\nexport interface SubscriptionResponse {\n  id: number;\n  accountId: number;\n  subscriptionPlanId: number;\n  planName: string;\n  planDescription: string;\n  maxSize: number;\n  price: number;\n  startDate: string;\n  endDate: string;\n  status: SubscriptionStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 邀请状态枚举\n */\nexport enum InvitationStatus {\n  PENDING = 'PENDING',\n  ACCEPTED = 'ACCEPTED',\n  REJECTED = 'REJECTED',\n  EXPIRED = 'EXPIRED',\n  CANCELLED = 'CANCELLED'\n}\n\n/**\n * 团队邀请响应\n */\nexport interface TeamInvitationResponse {\n  id: number;\n  teamId: number;\n  teamName: string;\n  inviterId: number;\n  inviterName: string;\n  inviterEmail: string;\n  inviteeEmail: string;\n  inviteeId?: number;\n  inviteeName?: string;\n  status: InvitationStatus;\n  invitedAt: string;\n  respondedAt?: string;\n  expiresAt: string;\n  message?: string;\n  createdAt: string;\n  updatedAt: string;\n  isExpired: boolean;\n  canBeResponded: boolean;\n  canBeCancelled: boolean;\n  invitationLink?: string; // 邀请链接\n}\n\n/**\n * 响应邀请请求\n */\nexport interface RespondInvitationRequest {\n  accept: boolean;\n  message?: string;\n}\n\n// ============= 实体类型 =============\n\n/**\n * 账户实体\n */\nexport interface Account {\n  id: number;\n  email: string;\n  name: string;\n  defaultSubscriptionPlanId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n\n\n/**\n * 团队实体\n */\nexport interface Team {\n  id: number;\n  name: string;\n  description?: string;\n  createdBy: number;\n  isDeleted: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 团队角色枚举\n */\nexport enum TeamRole {\n  TEAM_CREATOR = 'TEAM_CREATOR',\n  TEAM_MEMBER = 'TEAM_MEMBER'\n}\n\n/**\n * 团队成员实体\n */\nexport interface TeamMember {\n  id: number;\n  teamId: number;\n  accountId: number;\n  /** @deprecated 使用 role 字段替代 */\n  isCreator: boolean;\n  role: TeamRole;\n  assignedAt: string;\n  lastAccessTime: string;\n  isActive: boolean;\n  isDeleted: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 订阅套餐实体\n */\nexport interface SubscriptionPlan {\n  id: number;\n  name: string;\n  description: string;\n  maxSize: number;\n  price: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 账户订阅实体\n */\nexport interface AccountSubscription {\n  id: number;\n  accountId: number;\n  subscriptionPlanId: number;\n  startDate: string;\n  endDate: string;\n  status: SubscriptionStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n\n\n// ============= 用户统计相关类型 =============\n\n/**\n * 用户个人统计数据响应\n */\nexport interface UserPersonalStatsResponse {\n  vehicles: number;\n  personnel: number;\n  warnings: number;\n  alerts: number;\n}\n\n/**\n * 团队统计数据\n */\nexport interface TeamStatsData {\n  vehicles: number;\n  personnel: number;\n  expiring: number;\n  overdue: number;\n}\n\n/**\n * 用户详细信息响应\n */\nexport interface UserProfileDetailResponse {\n  name: string;\n  position: string;\n  email: string;\n  telephone: string;\n  registerDate: string;\n  lastLoginTime: string;\n  lastLoginTeam: string;\n  teamCount: number;\n  avatar?: string;\n}\n\n// ============= TODO相关类型 =============\n\n/**\n * TODO响应\n */\nexport interface TodoResponse {\n  id: number;\n  title: string;\n  description?: string;\n  status: number; // 0-未完成，1-已完成\n  priority: number; // 1-低，2-中，3-高\n  userId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 创建TODO请求\n */\nexport interface CreateTodoRequest {\n  title: string;\n  description?: string;\n  priority: number;\n}\n\n/**\n * 更新TODO请求\n */\nexport interface UpdateTodoRequest {\n  title?: string;\n  description?: string;\n  status?: number;\n  priority?: number;\n}\n\n/**\n * TODO统计信息响应\n */\nexport interface TodoStatsResponse {\n  highPriorityCount: number;\n  mediumPriorityCount: number;\n  lowPriorityCount: number;\n  totalCount: number;\n  completedCount: number;\n  completionPercentage: number;\n}\n\n// ============= 邀请链接相关类型 =============\n\n/**\n * 通过邀请链接接受邀请请求\n */\nexport interface AcceptInvitationByLinkRequest {\n  name?: string; // 新用户注册时需要\n  email?: string; // 新用户注册时需要\n  password?: string; // 新用户注册时需要\n  message?: string; // 响应消息\n}\n\n/**\n * 通过邀请链接接受邀请响应\n */\nexport interface AcceptInvitationByLinkResponse {\n  success: boolean;\n  teamId?: number;\n  teamName?: string;\n  userId?: number;\n  isNewUser?: boolean;\n  nextAction?: string;\n  accessToken?: string; // 自动登录令牌（新用户）\n  errorMessage?: string;\n}\n\n/**\n * 邀请信息响应\n */\nexport interface InvitationInfoResponse {\n  success: boolean;\n  teamId?: number;\n  teamName?: string;\n  inviterName?: string;\n  message?: string;\n  invitedAt?: string;\n  expiresAt?: string;\n  isExpired?: boolean;\n  canBeResponded?: boolean;\n  errorMessage?: string;\n}\n\n/**\n * 发送邀请响应中的邀请信息\n */\nexport interface InvitationInfo {\n  email: string;\n  invitationLink: string;\n  success: boolean;\n  errorMessage?: string;\n}\n\n/**\n * 发送邀请响应\n */\nexport interface SendInvitationResponse {\n  totalCount: number;\n  successCount: number;\n  failureCount: number;\n  invitations: InvitationInfo[];\n}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBCqND;;;;eAAA,uBAAA;;sBA2BA;;;;;;eAAA,qBAAA;;sBA0EA;;;eAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;ID1TE;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}